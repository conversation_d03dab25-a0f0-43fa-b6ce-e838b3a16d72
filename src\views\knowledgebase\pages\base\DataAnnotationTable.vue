<template>
  <div class="data-annotation-container">
    <!-- 左侧数据源树 -->
    <div class="left-panel" :style="{ width: leftPanelWidth + 'px' }">
      <div class="panel-header">
        <h3>数据库</h3>
      </div>
      <div class="tree-container">
        <el-tree
          ref="treeRef"
          :data="treeData"
          :props="treeProps"
          :load="loadNode"
          lazy
          node-key="id"
          :expand-on-click-node="false"
          @node-click="handleNodeClick"
        >
          <template #default="{ node, data }">
            <span class="tree-node">
              {{ data.sqlType.toLowerCase() }}
              
              <el-icon class="node-icon">
                <Iconfont
                style="margin-right: 6px"
                :size="24"
                :name="data.sqlType?.toLowerCase()"
                color="#409eff"
              />
                <!-- <component :is="getNodeIcon(data.type)" /> -->
              </el-icon>
              <span class="node-label">{{ node.label }}</span>
            </span>
          </template>
        </el-tree>
      </div>
    </div>

    <!-- 分割线 -->
    <div 
      class="splitter" 
      @mousedown="startResize"
    ></div>

    <!-- 右侧数据标注表格 -->
    <div class="right-panel" :style="{ width: rightPanelWidth + 'px' }">
      <div class="panel-header">
        <h3>{{ selectedTableName ? `${selectedTableName} - AI注释` : 'AI注释' }}</h3>
        <div class="header-actions">
          <el-button 
            type="primary" 
            :disabled="!hasChanges"
            @click="saveAnnotations"
          >
            保存
          </el-button>
          <el-button @click="refreshAnnotations">刷新</el-button>
        </div>
      </div>
      
      <div class="table-container">
        <el-table
          ref="tableRef"
          :data="tableColumns"
          v-loading="loading"
          height="100%"
          @cell-dblclick="handleCellEdit"
        >
          <el-table-column prop="columnName" label="列名(注释)" width="200">
            <template #default="{ row }">
              <div class="column-info">
                <div class="column-name">{{ row.columnName }}</div>
                <div class="column-comment" v-if="row.originalComment">
                  {{ row.originalComment }}
                </div>
              </div>
            </template>
          </el-table-column>
          
          <el-table-column prop="chineseName" label="自定义中文名称" width="200">
            <template #default="{ row, $index }">
              <div v-if="row.isEditing && editingCell.rowIndex === $index && editingCell.column === 'chineseName'">
                <el-input
                  ref="editInputRef"
                  v-model="row.chineseName"
                  @blur="handleCellSave(row, $index, 'chineseName')"
                  @keyup.enter="handleCellSave(row, $index, 'chineseName')"
                  @keyup.esc="handleCellCancel(row, $index)"
                  size="small"
                  placeholder="请输入中文名称"
                />
              </div>
              <div v-else class="editable-cell" @dblclick="handleCellEdit(row, null, null, $index, 'chineseName')">
                <span v-if="row.chineseName" class="cell-content">{{ row.chineseName }}</span>
                <span v-else class="cell-placeholder">双击编辑</span>
              </div>
            </template>
          </el-table-column>
          
          <el-table-column prop="showInDataset" label="示例数据集" width="120" align="center">
            <template #default="{ row }">
              <el-switch
                v-model="row.showInDataset"
                @change="handleDatasetChange(row)"
                active-color="#409eff"
                inactive-color="#dcdfe6"
              />
            </template>
          </el-table-column>

          <el-table-column prop="showInNumber" label="示例数据" width="120" align="center">
            <template #default="{ row }">
              <el-switch
                v-model="row.showInNumber"
                @change="handleNumberChange(row)"
                active-color="#409eff"
                inactive-color="#dcdfe6"
              />
            </template>
          </el-table-column>

          <el-table-column prop="sortOrder" label="排序" width="100" align="center">
            <template #default="{ row }">
              <span class="sort-order">{{ row.sortOrder || '-' }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, nextTick, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Coin, 
  Folder, 
  Document, 
  Grid 
} from '@element-plus/icons-vue'
import { AxiosApiService } from './../../../chatagent/api/axios-api-service'
import Iconfont from '/@/components/Iconfont.vue';
// 树形数据相关
interface TreeNode {
  id: string
  label: string
  type: 'datasource' | 'database' | 'schema' | 'table'
  children?: TreeNode[]
  isLeaf?: boolean
  dataSourceId?: number
  databaseName?: string
  schemaName?: string
  tableName?: string
}

// 表格列数据
interface TableColumn {
  columnName: string
  originalComment?: string
  chineseName?: string
  showInDataset: boolean
  showInNumber: boolean
  sortOrder?: number
  isEditing?: boolean
  originalData?: any
}

// 编辑状态
interface EditingCell {
  rowIndex: number
  column: string
}

// 响应式数据
const treeRef = ref()
const tableRef = ref()
const editInputRef = ref()
const treeData = ref<TreeNode[]>([])
const tableColumns = ref<TableColumn[]>([])
const loading = ref(false)
const selectedTableName = ref('')
const editingCell = reactive<EditingCell>({ rowIndex: -1, column: '' })

// 面板宽度控制
const leftPanelWidth = ref(200)
const rightPanelWidth = computed(() => window.innerWidth - leftPanelWidth.value - 10)
const isResizing = ref(false)

// 树形配置
const treeProps = {
  children: 'children',
  label: 'label',
  isLeaf: 'isLeaf'
}

// 计算是否有变更
const hasChanges = computed(() => {
  return tableColumns.value.some(col => {
    const original = col.originalData
    return original && (
      col.chineseName !== original.chineseName ||
      col.showInDataset !== original.showInDataset ||
      col.showInNumber !== original.showInNumber
    )
  })
})

// 获取节点图标
const getNodeIcon = (type: string) => {
  switch (type) {
    case 'datasource':
      return Folder
    case 'database':
      return Coin
    case 'schema':
      return Folder
    case 'table':
      return Grid
    default:
      return Document
  }
}

// 懒加载树节点
const loadNode = async (node: any, resolve: Function) => {
  if (node.level === 0) {
    // 加载数据源
    try {
      const dataSources = await AxiosApiService.getDataSourceList()
      const nodes = dataSources.map(ds => ({
        id: `ds_${ds.id}`,
        label: ds.name,
        type: 'datasource' as const,
        dataSourceId: ds.id,
        sqlType: ds.type,
        isLeaf: false
      }))
      resolve(nodes)
    } catch (error) {
      console.error('加载数据源失败:', error)
      resolve([])
    }
  } else if (node.data.type === 'datasource') {
    // 加载数据库
    try {
      const databases = await AxiosApiService.getDatabaseList(node.data.dataSourceId)
      const nodes = databases.map(db => ({
        id: `db_${node.data.dataSourceId}_${db.name}`,
        label: db.name,
        type: 'database' as const,
        dataSourceId: node.data.dataSourceId,
        databaseName: db.name,
        isLeaf: false
      }))
      resolve(nodes)
    } catch (error) {
      console.error('加载数据库失败:', error)
      resolve([])
    }
  } else if (node.data.type === 'database') {
    // 加载Schema
    try {
      const schemas = await AxiosApiService.getSchemaList(
        node.data.dataSourceId, 
        node.data.databaseName
      )
      const nodes = schemas.map(schema => ({
        id: `schema_${node.data.dataSourceId}_${node.data.databaseName}_${schema.name}`,
        label: schema.name,
        type: 'schema' as const,
        dataSourceId: node.data.dataSourceId,
        databaseName: node.data.databaseName,
        schemaName: schema.name,
        isLeaf: false
      }))
      resolve(nodes)
    } catch (error) {
      console.error('加载Schema失败:', error)
      resolve([])
    }
  } else if (node.data.type === 'schema') {
    // 加载数据表
    try {
      const tables = await AxiosApiService.getTableList(
        node.data.dataSourceId,
        node.data.databaseName,
        node.data.schemaName
      )
      console.log('tables:', tables)
      const nodes = tables.data.map((table: any) => ({
        id: `table_${node.data.dataSourceId}_${node.data.databaseName}_${node.data.schemaName}_${table.name}`,
        label: table.name,
        type: 'table' as const,
        dataSourceId: node.data.dataSourceId,
        databaseName: node.data.databaseName,
        schemaName: node.data.schemaName,
        tableName: table.name,
        isLeaf: true
      }))
      resolve(nodes)
    } catch (error) {
      console.error('加载数据表失败:', error)
      resolve([])
    }
  }
}

// 处理节点点击
const handleNodeClick = async (data: TreeNode) => {
  if (data.type === 'table') {
    selectedTableName.value = data.tableName || ''
    currentTableNode.value = data
    await loadTableColumns(data)
  }
}

// 加载表格列信息
const loadTableColumns = async (tableNode: TreeNode) => {
  loading.value = true
  try {
    // 调用获取表字段信息的API
    const columns = await AxiosApiService.getTableColumns(
      tableNode.dataSourceId!,
      tableNode.databaseName!,
      tableNode.schemaName!,
      tableNode.tableName!
    )

    // 获取已保存的数据标注
    let savedAnnotations: any[] = []
    try {
      savedAnnotations = await AxiosApiService.getDataAnnotations(
        tableNode.dataSourceId!,
        tableNode.databaseName!,
        tableNode.schemaName!,
        tableNode.tableName!
      )
    } catch (error) {
      console.warn('获取数据标注失败，使用默认值:', error)
    }

    // 创建标注映射
    const annotationMap = new Map()
    savedAnnotations.forEach(annotation => {
      annotationMap.set(annotation.columnName, annotation)
    })

    tableColumns.value = columns.map((col, index) => {
      const savedAnnotation = annotationMap.get(col.columnName)
      const chineseName = savedAnnotation?.chineseName || col.comment || ''
      const showInDataset = savedAnnotation?.showInDataset ?? true
      const showInNumber = savedAnnotation?.showInNumber ?? true

      return {
        columnName: col.columnName,
        originalComment: col.comment,
        chineseName,
        showInDataset,
        showInNumber,
        sortOrder: savedAnnotation?.sortOrder || index + 1,
        isEditing: false,
        originalData: {
          chineseName,
          showInDataset,
          showInNumber
        }
      }
    })
  } catch (error) {
    console.error('加载表字段失败:', error)
    ElMessage.error('加载表字段失败')

    // 如果API失败，使用模拟数据作为后备
    const mockColumns = [
      { columnName: 'ID', originalComment: '主键ID' },
      { columnName: 'SCHOOL', originalComment: '学校' },
      { columnName: 'SEX', originalComment: '性别' },
      { columnName: 'AGE', originalComment: '年龄' },
      { columnName: 'ADDRESS', originalComment: '地址' },
      { columnName: 'FAMSIZE', originalComment: '家庭规模' },
      { columnName: 'PSTATUS', originalComment: '父母状态' },
      { columnName: 'MEDU', originalComment: '母亲教育' },
      { columnName: 'FEDU', originalComment: '父亲教育' },
      { columnName: 'MJOB', originalComment: '母亲工作' },
      { columnName: 'FJOB', originalComment: '父亲工作' },
      { columnName: 'REASON', originalComment: '选择学校原因' },
      { columnName: 'GUARDIAN', originalComment: '监护人' },
      { columnName: 'TRAVELTIME', originalComment: '上学时间' },
      { columnName: 'STUDYTIME', originalComment: '学习时间' },
      { columnName: 'FAILURES', originalComment: '失败次数' }
    ]

    tableColumns.value = mockColumns.map((col, index) => ({
      ...col,
      chineseName: col.originalComment,
      showInDataset: true,
      showInNumber: true,
      sortOrder: index + 1,
      isEditing: false,
      originalData: {
        chineseName: col.originalComment,
        showInDataset: true,
        showInNumber: true
      }
    }))
  } finally {
    loading.value = false
  }
}

// 处理单元格双击编辑
const handleCellEdit = (row: TableColumn, column: any, cell: any, rowIndex: number, columnKey: string) => {
  if (columnKey === 'chineseName') {
    editingCell.rowIndex = rowIndex
    editingCell.column = columnKey
    row.isEditing = true
    
    nextTick(() => {
      if (editInputRef.value) {
        editInputRef.value.focus()
      }
    })
  }
}

// 保存单元格编辑
const handleCellSave = (row: TableColumn, rowIndex: number, columnKey: string) => {
  row.isEditing = false
  editingCell.rowIndex = -1
  editingCell.column = ''
}

// 取消单元格编辑
const handleCellCancel = (row: TableColumn, rowIndex: number) => {
  row.isEditing = false
  editingCell.rowIndex = -1
  editingCell.column = ''
  // 恢复原值
  if (row.originalData) {
    row.chineseName = row.originalData.chineseName
  }
}

// 处理数据集开关变化
const handleDatasetChange = (row: TableColumn) => {
  // 可以在这里添加额外的逻辑
}

// 处理数据开关变化
const handleNumberChange = (row: TableColumn) => {
  // 可以在这里添加额外的逻辑
}

// 保存标注
const saveAnnotations = async () => {
  if (!selectedTableName.value) {
    ElMessage.warning('请先选择数据表')
    return
  }

  try {
    // 获取当前选中的表节点信息
    const currentTableNode = getCurrentTableNode()
    if (!currentTableNode) {
      ElMessage.error('无法获取表信息')
      return
    }

    // 构建标注数据
    const annotations = tableColumns.value.map(col => ({
      dataSourceId: currentTableNode.dataSourceId!,
      databaseName: currentTableNode.databaseName!,
      schemaName: currentTableNode.schemaName!,
      tableName: currentTableNode.tableName!,
      columnName: col.columnName,
      chineseName: col.chineseName,
      showInDataset: col.showInDataset,
      showInNumber: col.showInNumber,
      sortOrder: col.sortOrder
    }))

    // 调用保存API
    await AxiosApiService.saveDataAnnotations(annotations)
    ElMessage.success('保存成功')

    // 更新原始数据
    tableColumns.value.forEach(col => {
      if (col.originalData) {
        col.originalData.chineseName = col.chineseName
        col.originalData.showInDataset = col.showInDataset
        col.originalData.showInNumber = col.showInNumber
      }
    })
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败')
  }
}

// 获取当前表节点
const getCurrentTableNode = (): TreeNode | null => {
  // 这里需要保存当前选中的表节点信息
  // 可以通过遍历树或者保存在组件状态中
  return currentTableNode.value
}

// 当前选中的表节点
const currentTableNode = ref<TreeNode | null>(null)

// 刷新标注
const refreshAnnotations = () => {
  if (selectedTableName.value && currentTableNode.value) {
    loadTableColumns(currentTableNode.value)
  }
}

// 开始调整大小
const startResize = (e: MouseEvent) => {
  isResizing.value = true
  document.addEventListener('mousemove', handleResize)
  document.addEventListener('mouseup', stopResize)
  e.preventDefault()
}

// 处理调整大小
const handleResize = (e: MouseEvent) => {
  if (isResizing.value) {
    const newWidth = e.clientX
    if (newWidth >= 200 && newWidth <= window.innerWidth - 400) {
      leftPanelWidth.value = newWidth
    }
  }
}

// 停止调整大小
const stopResize = () => {
  isResizing.value = false
  document.removeEventListener('mousemove', handleResize)
  document.removeEventListener('mouseup', stopResize)
}

// 组件卸载时清理事件监听
onUnmounted(() => {
  document.removeEventListener('mousemove', handleResize)
  document.removeEventListener('mouseup', stopResize)
})
</script>

<style scoped lang="scss">
.data-annotation-container {
  display: flex;
  height: calc(100vh - 200px);
  background-color: #f5f7fa;
}

.left-panel {
  background-color: #fff;
  border-right: 1px solid #e6e6e6;
  display: flex;
  flex-direction: column;
  min-width: 200px;
}

.right-panel {
  background-color: #fff;
  display: flex;
  flex-direction: column;
  flex: 1;
  min-width: 400px;
}

.panel-header {
  padding: 16px 20px;
  border-bottom: 1px solid #e6e6e6;
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #303133;
  }
}

.header-actions {
  display: flex;
  gap: 8px;
}

.tree-container {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.tree-node {
  display: flex;
  align-items: center;
  
  .node-icon {
    margin-right: 8px;
    color: #606266;
  }
  
  .node-label {
    font-size: 14px;
    color: #303133;
  }
}

.splitter {
  width: 6px;
  background-color: #e6e6e6;
  cursor: col-resize;
  position: relative;
  
  &:hover {
    background-color: #409eff;
  }
  
  &::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 2px;
    height: 20px;
    background-color: #fff;
    border-radius: 1px;
  }
}

.table-container {
  flex: 1;
  padding: 16px;
}

.column-info {
  .column-name {
    font-weight: 600;
    color: #303133;
  }
  
  .column-comment {
    font-size: 12px;
    color: #909399;
    margin-top: 2px;
  }
}

.editable-cell {
  min-height: 20px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;

  &:hover {
    background-color: #f5f7fa;
  }

  .cell-content {
    color: #303133;
  }

  .cell-placeholder {
    color: #c0c4cc;
    font-style: italic;
  }
}

.sort-order {
  color: #606266;
  font-weight: 500;
}

:deep(.el-tree-node__content) {
  height: 36px;
}

:deep(.el-table .cell) {
  padding: 8px 12px;
}

:deep(.el-table th) {
  background-color: #fafafa;
  color: #303133;
  font-weight: 600;
}

:deep(.el-table tr:hover > td) {
  background-color: #f5f7fa;
}

:deep(.el-table__body tr.current-row > td) {
  background-color: #ecf5ff;
}

:deep(.el-switch.is-checked .el-switch__core) {
  background-color: #409eff;
}

:deep(.el-input__inner) {
  border-radius: 4px;
}

.panel-header .header-actions .el-button {
  border-radius: 4px;
}

.panel-header .header-actions .el-button--primary {
  background-color: #409eff;
  border-color: #409eff;
}

.panel-header .header-actions .el-button--primary:hover {
  background-color: #66b1ff;
  border-color: #66b1ff;
}
</style>
